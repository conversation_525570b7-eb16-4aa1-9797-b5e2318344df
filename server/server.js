// server/server.js - Mock server based on activity.proto
import { fileURLToPath } from 'node:url';
import { dirname, join } from 'node:path';
import process from 'node:process';
import express from 'express';
import cors from 'cors';
import morgan from 'morgan';
import dayjs from 'dayjs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = 3002;

// Middleware
app.use(cors());
app.use(express.json());
app.use(morgan('dev'));

// --- Helper Functions for Mock Data Generation ---
function generateUserInfo(uid, _usernamePrefix = 'User') {
    return {
        uid,
        username: `tt110200509`,
        alias: `Alias_${uid}`,
        nickname: `Nickname ${uid}`,
        sex: uid % 2 === 0 ? 1 : 2, // 1 for male, 2 for female
        guildInfo: {
            name: `Guild_${String.fromCharCode(65 + (uid % 5))}`,
            guildId: 100 + (uid % 10),
            displayId: 1000 + (uid % 100),
        },
        role: uid % 3, // 0, 1, 2
    };
}

function generateChannelInfo(channelId) {
    const statusValues = [0, 1, 2, 3, 4]; // LEAVE, STAY, WATCH, LIVE, PK
    return {
        channelId,
        status: statusValues[channelId % statusValues.length],
    };
}

// 验证请求参数
function validateRequest(reqBody, requiredFields = []) {
    const errors = [];

    if (!reqBody) {
        errors.push('Request body is required');
        return errors;
    }

    requiredFields.forEach((field) => {
        if (reqBody[field] === undefined || reqBody[field] === null) {
            errors.push(`Field '${field}' is required`);
        }
    });

    return errors;
}

// --- Mock Data Definitions ---
const mockResponses = {
    // 初始化接口 - 符合 InitResp 定义
    init: _reqBody => ({
        serverTime: Math.floor(Date.now() / 1000),
        startTime: Math.floor(dayjs().subtract(7, 'day').valueOf() / 1000),
        endTime: Math.floor(dayjs().add(7, 'day').valueOf() / 1000),
        openCount: Math.floor(Math.random() * 5) + 1,
    }),

    // 获取购买记录 - 符合 GetBuyRecordResp 定义
    getBuyRecord: (reqBody) => {
        const { page = 1, pageSize = 10 } = reqBody;
        const totalItems = 50;
        const list = [];

        for (let i = 0; i < Math.min(pageSize, 10); i++) {
            const itemIndex = (page - 1) * pageSize + i;
            if (itemIndex >= totalItems)
                break;

            list.push({
                time: Math.floor(Date.now() / 1000) - itemIndex * 3600,
                rewardStr: `奖励${itemIndex + 1}: ${Math.floor(Math.random() * 1000) + 100}豆`,
            });
        }

        return { list, total: totalItems };
    },

    // 骑士每日发送T豆 - 符合 GetKnightDailySendTBeanResp 定义
    getKnightDailySendTBean: _reqBody => ({
        value: Math.floor(Math.random() * 1000) + 100,
    }),

    // 获取骑士荣誉 - 符合 GetKnightHonorResp 定义
    getKnightHonor: (_reqBody) => {
        const issues = [];

        // 生成3个期数的数据
        for (let issue = 1; issue <= 3; issue++) {
            const issueList = [];

            // 每个期数生成10个骑士荣誉项
            for (let i = 0; i < 10; i++) {
                const uid = 2416206 + i;
                const rank = i + 1;
                const value = Math.floor(Math.random() * 10000) + 1000;

                // 生成MVP信息列表
                const mvpInfoList = [];
                for (let j = 0; j < 5; j++) {
                    mvpInfoList.push({
                        rank: j + 1,
                        value: Math.floor(Math.random() * 5000) + 500,
                        valueHuman: `${Math.floor(Math.random() * 5000) + 500}豆`,
                        userInfo: generateUserInfo(uid + j + 100),
                    });
                }

                issueList.push({
                    uid,
                    rank,
                    rankHuman: `${rank}`,
                    value,
                    valueHuman: `${value}收藏石`,
                    ltPrevValue: value + 100,
                    ltPrevValueHuman: `${value + 100}收藏石`,
                    gtNextValue: value - 100,
                    gtNextValueHuman: `${value - 100}收藏石`,
                    userInfo: generateUserInfo(uid),
                    channelInfo: generateChannelInfo(uid + 1000),
                    mvpInfoList,
                });
            }

            issues.push({
                issue,
                list: issueList,
            });
        }

        return { list: issues };
    },

    // 获取骑士排行 - 符合 GetKnightRankResp 定义
    getKnightRank: (reqBody) => {
        const { page = 1, pageSize = 20, uid } = reqBody;
        const totalItems = 100;
        const list = [];

        for (let i = 0; i < Math.min(pageSize, 20); i++) {
            const rank = (page - 1) * pageSize + i + 1;
            if (rank > totalItems)
                break;

            const userUid = (uid || 123456) + i + 100;
            const value = 100000 - rank * 1000;

            // 生成MVP信息列表
            const mvpInfoList = [];
            for (let j = 0; j < 3; j++) {
                mvpInfoList.push({
                    rank: j + 1,
                    value: Math.floor(Math.random() * 3000) + 500,
                    valueHuman: `${Math.floor(Math.random() * 3000) + 500}豆`,
                    userInfo: generateUserInfo(userUid + j + 50),
                });
            }

            list.push({
                uid: userUid,
                rank,
                rankHuman: `${rank}`,
                value,
                valueHuman: `${value}收藏石`,
                ltPrevValue: value + 100,
                ltPrevValueHuman: `${value + 100}收藏石`,
                gtNextValue: value - 100,
                gtNextValueHuman: `${value - 100}收藏石`,
                userInfo: generateUserInfo(userUid),
                channelInfo: generateChannelInfo(userUid + 1000),
                mvpInfoList,
            });
        }

        return {
            list,
            total: totalItems,
            self: uid
                ? {
                        uid,
                        rank: 15,
                        rankHuman: '第15名',
                        value: 85000,
                        valueHuman: '85000收藏石',
                        ltPrevValue: 85100,
                        ltPrevValueHuman: '85100收藏石',
                        gtNextValue: 84900,
                        gtNextValueHuman: '84900收藏石',
                        userInfo: generateUserInfo(uid),
                        channelInfo: generateChannelInfo(uid + 1000),
                        mvpInfoList: [{
                            rank: 1,
                            value: 2500,
                            valueHuman: '2500豆',
                            userInfo: generateUserInfo(uid + 50),
                        }],
                    }
                : null,
        };
    },

    // 专家每日接收T豆 - 符合 GetExpertDailyReceiveTBeanResp 定义
    getExpertDailyReceiveTBean: _reqBody => ({
        value: Math.floor(Math.random() * 800) + 200,
        userInfo: generateUserInfo(123456),
    }),

    // 获取专家排行 - 符合 GetExpertRankResp 定义
    getExpertRank: (reqBody) => {
        const { page = 1, pageSize = 20, uid } = reqBody;
        const totalItems = 150;
        const list = [];

        for (let i = 0; i < Math.min(pageSize, 20); i++) {
            const rank = (page - 1) * pageSize + i + 1;
            if (rank > totalItems)
                break;

            const userUid = (uid || 123456) + i + 200;
            const value = 200000 - rank * 2000;

            // 生成MVP信息列表
            const mvpInfoList = [];
            for (let j = 0; j < 4; j++) {
                mvpInfoList.push({
                    rank: j + 1,
                    value: Math.floor(Math.random() * 4000) + 1000,
                    valueHuman: `${Math.floor(Math.random() * 4000) + 1000}豆`,
                    userInfo: generateUserInfo(userUid + j + 75),
                });
            }

            list.push({
                uid: userUid,
                rank,
                rankHuman: `${rank}`,
                value,
                valueHuman: `${value}收藏石`,
                ltPrevValue: value + 200,
                ltPrevValueHuman: `${value + 200}收藏石`,
                gtNextValue: value - 200,
                gtNextValueHuman: `${value - 200}收藏石`,
                userInfo: generateUserInfo(userUid),
                channelInfo: generateChannelInfo(userUid + 2000),
                mvpInfoList,
            });
        }

        return {
            list,
            total: totalItems,
            self: uid
                ? {
                        uid,
                        rank: 25,
                        rankHuman: '第25名',
                        value: 150000,
                        valueHuman: '150000收藏石',
                        ltPrevValue: 150200,
                        ltPrevValueHuman: '150200收藏石',
                        gtNextValue: 149800,
                        gtNextValueHuman: '149800收藏石',
                        userInfo: generateUserInfo(uid),
                        channelInfo: generateChannelInfo(uid + 2000),
                        mvpInfoList: [{
                            rank: 1,
                            value: 3500,
                            valueHuman: '3500豆',
                            userInfo: generateUserInfo(uid + 75),
                        }],
                    }
                : null,
        };
    },

};

// --- API Route Definitions ---
// 根据前端 API 需求定义的端点
const apiEndpoints = {
    // 初始化
    init: { method: 'post', reqType: 'InitReq', respType: 'init' },
    // 获取购买记录
    getBuyRecord: { method: 'post', reqType: 'GetBuyRecordReq', respType: 'getBuyRecord' },
    // 骑士每日发送T豆
    getKnightDailySendTBean: { method: 'post', reqType: 'InitReq', respType: 'getKnightDailySendTBean' },
    // 获取骑士荣誉
    getKnightHonor: { method: 'post', reqType: 'Empty', respType: 'getKnightHonor' },
    // 获取骑士排行
    getKnightRank: { method: 'post', reqType: 'GetKnightRankReq', respType: 'getKnightRank' },
    // 专家每日接收T豆
    getExpertDailyReceiveTBean: { method: 'post', reqType: 'InitReq', respType: 'getExpertDailyReceiveTBean' },
    // 获取专家排行
    getExpertRank: { method: 'post', reqType: 'GetExpertRankReq', respType: 'getExpertRank' },
};

// 请求参数验证规则
const validationRules = {
    InitReq: ['uid'],
    GetBuyRecordReq: [],
    GetKnightRankReq: [],
    GetExpertRankReq: [],
    Empty: [],
};

Object.entries(apiEndpoints).forEach(([rpcName, config]) => {
    const routePath = `/activity.Activity/${rpcName}`;
    const responseGenerator = mockResponses[config.respType];

    if (!responseGenerator) {
        console.error(`No response generator found for ${config.respType} (RPC: ${rpcName})`);
        return;
    }

    if (config.method === 'get') {
        app.get(routePath, (req, res) => {
            try {
                if (process.env.NODE_ENV === 'development') {
                    console.log(`GET ${routePath}`);
                }

                const mockResponseData = responseGenerator(req.query);
                res.json({
                    code: 0,
                    msg: 'success',
                    data: mockResponseData,
                });
            }
            catch (error) {
                console.error(`Error in GET ${routePath}:`, error);
                res.status(500).json({
                    code: -1,
                    msg: 'Internal server error',
                    data: null,
                });
            }
        });
    }
    else if (config.method === 'post') {
        app.post(routePath, (req, res) => {
            try {
                if (process.env.NODE_ENV === 'development') {
                    console.log(`POST ${routePath} with body:`, req.body);
                }

                // 验证请求参数
                const requiredFields = validationRules[config.reqType] || [];
                const validationErrors = validateRequest(req.body, requiredFields);

                if (validationErrors.length > 0) {
                    return res.status(400).json({
                        code: 400,
                        msg: `Validation failed: ${validationErrors.join(', ')}`,
                        data: null,
                    });
                }

                const mockResponseData = responseGenerator(req.body);
                res.json({
                    code: 0,
                    msg: 'success',
                    data: mockResponseData,
                });
            }
            catch (error) {
                console.error(`Error in POST ${routePath}:`, error);
                res.status(500).json({
                    code: -1,
                    msg: 'Internal server error',
                    data: null,
                });
            }
        });
    }
});

// Serve static files (if any)
app.use(express.static(join(__dirname, 'public')));

// Start server
app.listen(PORT, () => {
    console.log(`Mock server is running on http://localhost:${PORT}`);
    console.log('Available RPC endpoints:');
    Object.entries(apiEndpoints).forEach(([rpcName, config]) => {
        console.log(`- ${config.method.toUpperCase()} /activity.Activity/${rpcName}`);
    });
});

export default app;
