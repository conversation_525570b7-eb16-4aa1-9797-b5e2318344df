// src/api/api.d.ts - API 类型定义文件

/** 基础请求类型 */
interface InitReq {
    uid: number;
}

/** 初始化响应 */
interface InitResp {
    /**服务器时间 */
    serverTime: number;
    /**活动开始时间 */
    startTime: number;
    /**活动结束时间 */
    endTime: number;
    /**开启次数 */
    openCount?: number;
}

/** 空请求类型 */
interface Empty {}

/** 用户信息 */
interface UserInfo {
    uid: number;
    username: string;
    alias: string;
    nickname: string;
    sex: number;
    guildInfo: GuildInfo;
    role: number;
}

/** 公会信息 */
interface GuildInfo {
    name: string;
    guildId: number;
    displayId: number;
}

/** 频道信息 */
interface ChannelInfo {
    channelId: number;
    status: number;
}

/** MVP信息 */
interface MvpInfo {
    rank: number;
    value: number;
    valueHuman: string;
    userInfo: UserInfo;
}

/** 购买记录请求 */
interface GetBuyRecordReq {
    uid?: number;
    page?: number;
    pageSize?: number;
}

/** 购买记录项 */
interface BuyRecordItem {
    time: number;
    rewardStr: string;
}

/** 购买记录响应 */
interface GetBuyRecordResp {
    list: BuyRecordItem[];
    total: number;
}

/** 骑士每日发送T豆响应 */
interface GetKnightDailySendTBeanResp {
    value: number;
}

/** 骑士荣誉项 */
interface KnightHonorItem {
    uid: number;
    rank: number;
    rankHuman: string;
    value: number;
    valueHuman: string;
    ltPrevValue: number;
    ltPrevValueHuman: string;
    gtNextValue: number;
    gtNextValueHuman: string;
    userInfo: UserInfo;
    channelInfo: ChannelInfo;
    mvpInfoList: MvpInfo[];
}

/** 骑士荣誉期数 */
interface KnightHonorIssue {
    issue: number;
    list: KnightHonorItem[];
}

/** 骑士荣誉响应 */
interface GetKnightHonorResp {
    list: KnightHonorIssue[];
}

/** 骑士排行请求 */
interface GetKnightRankReq {
    uid?: number;
    type?: number;
    page?: number;
    pageSize?: number;
}

/** 骑士排行响应 */
interface GetKnightRankResp {
    list: KnightHonorItem[];
    total?: number;
    currentUser?: KnightHonorItem;
}

/** 专家每日接收T豆响应 */
interface GetExpertDailyReceiveTBeanResp {
    value: number;
}

/** 专家排行请求 */
interface GetExpertRankReq {
    uid?: number;
    type?: number;
    page?: number;
    pageSize?: number;
}

/** 专家排行响应 */
interface GetExpertRankResp {
    list: KnightHonorItem[];
    total?: number;
    currentUser?: KnightHonorItem;
}

export type none = {};
