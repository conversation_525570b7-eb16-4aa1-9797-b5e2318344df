// 执行pb.cjs命令后会覆盖掉手写的代码，请谨慎操作!
import request from '../utils/request';
import getMockData from './mockData';

export const fetchApi = ({ proPrefix = '/activity.Activity/', api, data = {}, config = {} }) => {
    const { mock } = myWebview.params;
    if (mock)
        return to(getMockData(api, data));

    const url = `${proPrefix}/${api}`.replace('//', '/');
    return to(request.post(url, data, config));
};

/**
 * ********************************
 * *********  活动接口 *************
 * ********************************
 */
const REQUEST_API_MAP = {
    init: 'init',
    getBuyRecord: 'getBuyRecord',
    getKnightDailySendTBean: 'getKnightDailySendTBean',
    getKnightHonor: 'getKnightHonor',
    getKnightRank: 'getKnightRank',
    getExpertDailyReceiveTBean: 'getExpertDailyReceiveTBean',
    getExpertRank: 'getExpertRank',
};

/** @type {function(import('./api.d.ts').InitReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').InitResp},any]>} */
export const init = (data, config) => fetchApi({ api: REQUEST_API_MAP.init, data, config });

/** @type {function(import('./api.d.ts').GetBuyRecordReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').GetBuyRecordResp},any]>} */
export const getBuyRecord = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getBuyRecord, data, config });

/** @type {function(import('./api.d.ts').InitReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').GetKnightDailySendTBeanResp},any]>} */
export const getKnightDailySendTBean = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getKnightDailySendTBean, data, config });

/** @type {function(import('./api.d.ts').Empty):Promise<[{code:number,msg:string,data:import('./api.d.ts').GetKnightHonorResp},any]>} */
export const getKnightHonor = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getKnightHonor, data, config });

/** @type {function(import('./api.d.ts').GetKnightRankReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').GetKnightRankResp},any]>} */
export const getKnightRank = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getKnightRank, data, config });

/** @type {function(import('./api.d.ts').InitReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').GetExpertDailyReceiveTBeanResp},any]>} */
export const getExpertDailyReceiveTBean = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getExpertDailyReceiveTBean, data, config });

/** @type {function(import('./api.d.ts').GetExpertRankReq):Promise<[{code:number,msg:string,data:import('./api.d.ts').GetExpertRankResp},any]>} */
export const getExpertRank = (data, config) =>
    fetchApi({ api: REQUEST_API_MAP.getExpertRank, data, config });

export default {
    init,
    getBuyRecord,
    getKnightDailySendTBean,
    getKnightHonor,
    getKnightRank,
    getExpertDailyReceiveTBean,
    getExpertRank,
};
