import { jumpLink } from '@/utils/jsbridge';
import { formatTime, getAvatar, num2percent, omitTxt, omitValue, supplementDouble } from '@/utils';
import { rewardMap } from '@/config/reward';
import { env } from '@/config/url';

export const getRewardInfo = (id) => {
    const { rawReward } = parseUrlQuery();
    const info = rewardMap[id] ? { ...rewardMap[id] } : {};
    let imageUrl = '';
    try {
        imageUrl = requireImg(`rewards/${info.image}`);
        if (rawReward) {
            throw new Error('展示原始奖励，用 id 展示');
        }
    }
    catch {
        const canvas = document.createElement('canvas');
        // 绘制文字环境
        const context = canvas.getContext('2d');
        // 画布宽度
        canvas.width = 100;
        // 画布高度
        canvas.height = 100;

        // 设置水平对齐方式
        context.textAlign = 'center';
        // 设置垂直对齐方式
        context.textBaseline = 'middle';

        // 填充白色
        context.fillStyle = '#ffffff';
        // 绘制文字之前填充白色
        context.fillRect(0, 0, canvas.width, canvas.height);

        context.fillStyle = '#000000';
        context.font = '30px Arial';
        // 绘制文字（参数：要写的字，x坐标，y坐标）
        context.fillText(id, canvas.width / 2, canvas.height / 2);
        imageUrl = canvas.toDataURL('image/png');
    }

    return { ...info, imageUrl };
};

export function jumpToLink(activityId) {
    let domainProdPath = 'https://appcdn.52tt.com';
    let url = '';
    if (MARKET_ID === MARKET_ID_MAP.TT) {
        domainProdPath = 'https://appcdn.52tt.com';
    }
    else if (MARKET_ID === MARKET_ID_MAP.HY) {
        if (myWebview.isIOS()) {
            domainProdPath = 'https://appcdn.tses.net';
        }
        else domainProdPath = 'https://zy-appcdn.rzhushou.com';
    }
    else if (MARKET_ID === MARKET_ID_MAP.MK) {
        if (myWebview.isIOS()) {
            domainProdPath = 'https://appcdn.tingyou.fun';
        }
        else domainProdPath = 'https://appcdn.yuyue27.com';
    }
    url = `${domainProdPath}${activityId}`;
    if (myWebview.isInApp()) {
        jumpLink(url);
    }
    else {
        window.location.href = url;
    }
}

export function getPrice(reward, num) {
    const rewardInfo = getRewardInfo(reward?.id);
    const mark = rewardInfo?.mark;
    return `${(mark !== '包裹' ? mark : `${rewardInfo.price}豆`)}`;
};

export function getRewardMark({ id, mark }) {
    if (!mark)
        return '';
    const rewardInfo = getRewardInfo(id);
    const myMark = rewardInfo?.mark;
    if (myMark === '红V认证') {
        return ``;
    }
    return `${myMark !== '包裹' ? (rewardInfo?.special_type || '') + myMark : `${rewardInfo.price}豆`}`;
};

// 获取没有缓存、没有重定向的头像地址
export function getDirectAvatarUrl(username) {
    const map = {
        dev: username => `https://testing-avatar.ttyuyin.com/v2/${username}/ver-20221230133705/small?t=${Math.random()}${Date.now()}`,
        testing: username => `https://testing-avatar.ttyuyin.com/v2/${username}/ver-20221230133705/small?t=${Math.random()}${Date.now()}`,
        gray: username => `https://avatar.52tt.com/v2/${username}/ver-17f1c25943b0c8cb/small?t=${Math.random()}${Date.now()}`,
        prod: username => `https://avatar.52tt.com/v2/${username}/ver-17f1c25943b0c8cb/small?t=${Math.random()}${Date.now()}`,
    };
    return map[env](username);
}

// 获取图像base64
export async function getImageBase64(url) {
    return new Promise((resolve, reject) => {
        const image = new Image();
        image.onload = () => {
            const canvas = document.createElement('canvas');
            canvas.width = image.width;
            canvas.height = image.height;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(image, 0, 0, image.width, image.height);
            const ext = image.src.substring(image.src.lastIndexOf('.') + 1).toLowerCase();
            const dataURL = canvas.toDataURL(`image/${ext}`);
            resolve(dataURL);
        };
        image.onerror = (err) => {
            reject(err);
        };
        image.crossOrigin = 'Anonymous';
        image.src = url;
    });
}

// 获取头像base64
export async function getAvatarBase64(username) {
    return new Promise((resolve) => {
        const url = getDirectAvatarUrl(username);
        getImageBase64(url)
            .then((res) => {
                resolve(res);
            })
            .catch(() => {
                resolve('');
            });
    });
}

export const RULE_LINK = '/web/beluga-activity-6870a3d8b9deed99c33ffc45/index.html';

// 标准化用户数据格式
export function normalizeUserData(user, index) {
    return {
        uid: user?.uid || user?.userInfo?.uid,
        username: user?.username || user?.userInfo?.username || '虚位以待',
        nickname: user?.nickname || user?.userInfo?.nickname || '虚位以待',
        alias: user?.alias || user?.userInfo?.alias,
        value: user?.value || user?.amount || user?.score || 0,
        rank: user?.rankHuman || user?.rank || user?.ranking || index + 1,
        channelInfo: user?.channelInfo || user?.userInfo?.channelInfo,
        gtNextValueHuman: user?.gtNextValueHuman,
        ltPrevValueHuman: user?.ltPrevValueHuman,
        mvpInfoList: user?.mvpInfoList,
    };
}
