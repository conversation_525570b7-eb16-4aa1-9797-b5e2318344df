<template>
    <div class="flex flex-col items-center">
        <div
            :style=" { backgroundImage: `url(${requireImg('<EMAIL>')})` }"
            class="bg-default relative h-[218.5px] w-[373.5px] flex flex-col items-center"

        >
            <img
                class="absolute right-15 top-73 h-[19px] w-[63px]"
                src="@/assets/img/<EMAIL>"
                alt=""
                @click="useEventBus('record-modal').emit({ show: true })">
            <div

                class="mt-42 text-10 text-[#FFFFFF]">
                {{ remainTimeStr ? `活动倒计时:${remainTimeStr}` : '活动已结束' }}
            </div>
            <div
                class="mt-42 flex justify-center">
                <div
                    v-for="value in rwdList"
                    class="w-[120px] flex flex-col items-center"
                >
                    <div
                        :style="{ backgroundImage: `url(${requireImg('<EMAIL>')})` }"
                        class="bg-default flex-center relative h-[69px] w-[69px] flex"
                    >
                        <img
                            :src="value.imageUrl"
                            class="h-90% w-90% object-contain"
                            alt="">
                    </div>
                    <div class="mt-6 text-11 text-[#FFFFFF]">{{ value.name }}</div>
                </div>
            </div>
            <div
                :style="{ backgroundImage: `url(${requireImg('<EMAIL>')})` }"
                class="bg-default flex-center relative mt-8 h-[21px] w-[120.5px] flex text-11 text-[#fff]"

            >
                开通骑士团：{{ initData.openCount }}
            </div>
        </div>
        <div
            :style="{ backgroundImage: `url(${requireImg('<EMAIL>')})` }"
            class="bg-default relative mt-14 h-[436px] w-[375px] flex flex-col items-center"
        >
            <div class="text-mine mt-51 text-12">
                每日骑士给守护达人送礼达到指定值,可得专属礼物赠礼权
            </div>

            <div
                :style="{ backgroundImage: `url(${requireImg('<EMAIL>')})` }"
                class="bg-default flex-center relative relative mt-13 h-[101px] w-[101px] flex"
            >
                <img
                    :src="bigRewardInfo.imageUrl"
                    class="h-90% w-90%"
                    alt="">

                <img
                    class="absolute bottom-6 right-6 h-[22px] w-[89.5px]"
                    src="@/assets/img/<EMAIL>"
                    alt=""
                    @click="useEventBus('video-modal').emit({ show: true, url: 'https://obs-cdn.52tt.com/tt/fe-moss/web/20250715154658_53673154.mp4' })">
            </div>
            <div class="mt-6 text-11 text-[#FFFFFF]">{{ bigRewardInfo.name }}礼物</div>

            <!-- 任务列表 -->
            <div
                v-for="task in taskList"
                :key="task.target"
                :style="{ backgroundImage: `url(${requireImg('<EMAIL>')})` }"
                class="bg-default flex-center relative relative mt-11 h-[64.5px] w-[328.5px] flex text-13 text-[#FFFFFF]"
            >
                <div class="w-250">
                    达人骑士团成员送出任意礼物达{{ task.target }}豆，获得【{{ bigRewardInfo.name }}{{ task.rewardText }}】送礼权*{{ task.days }}天（{{ Math.min(value, task.target) }}/{{ task.target }}）
                </div>
                <img
                    v-if="Math.min(value, task.target) < task.target"
                    class="h-[24px] w-[63.5px]"
                    src="@/assets/img/<EMAIL>"
                    alt="">
                <img
                    v-else
                    class="h-[24px] w-[63.5px]"
                    src="@/assets/img/<EMAIL>"
                    alt="">
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import useInitStore from '@/stores/modules/use-init-store';
import { getRewardInfo } from '@/auto-imports/my-utils';
import { getKnightDailySendTBean } from '@/api';

const initStore = useInitStore();
dayjs.extend(duration);

const remainTimeStr
 = computed(() => {
     const curTime = dayjs.unix(initStore.serverTime).tz('Asia/Shanghai');
     const endTime = dayjs.unix(initStore.initData.endTime).tz('Asia/Shanghai');

     let remainStr = '';
     // 判断活动状态
     if (curTime.isBefore(endTime)) {
         const diff = endTime.diff(curTime, 'second');
         remainStr = dayjs.duration(Math.max(0, diff), 'second').format('D天HH时mm分ss秒');
     }
     return remainStr;
 });

const bigRewardInfo = getRewardInfo('B1');

const rwdList = [
    {
        ...getRewardInfo('G1'),
        name: '5天骑士体验卡*1',
    },
    {
        ...getRewardInfo('A1'),
        name: '守护之心个人铭牌*10天',
    },
];

// 任务列表配置
const taskList = [
    {
        target: 10000,
        days: 1,
        rewardText: '1W豆',
    },
    {
        target: 50000,
        days: 2,
        rewardText: '5W豆',
    },
    {
        target: 100000,
        days: 3,
        rewardText: '10W豆',
    },
];

const value = ref(0);

function getKnightDailySendTBeanData() {
    getKnightDailySendTBean().then(([res]) => {
        if (res.code !== 0) {
            return;
        }
        value.value = res.data.value;
    });
}

onMounted(() => {
    getKnightDailySendTBeanData();
});
</script>

<style lang="less" scoped>

</style>
