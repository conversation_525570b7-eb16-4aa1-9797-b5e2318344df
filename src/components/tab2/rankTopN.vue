<template>
    <div
        class="rank-n absolute flex-shrink-0"
        :class="[positionN[rank]]"
    >
        <div class="avatar">
            <img
                :src="getAvatar(info.username)"
                class="avatar"
            />
        </div>
        <div
            class="bg-default absolute left-0 top-0 h-full w-full flex flex-col items-center pt-14"
            :class="[`rank-${rank}`]"
        >
            <div
                class="avatar"
                @click="toPerson(info.username)"
            >
                <room-status-anchor
                    v-if="info.channelInfo?.channelId"
                    :cid="info.channelInfo?.channelId"
                    :status="info.channelInfo?.status"
                    :is-anchor="isAnchor"
                    class="room-status-anchor" />
            </div>
            <div class="nickname">{{ safeOmitTxt(info.nickname, 6) }}</div>
            <div
                v-if="isAnchor"
                class="text-11 text-[#fff]">
                ID:{{ info.alias }}
            </div>
            <div class="value">
                <div class="desc">{{ isAnchor ? '收礼值' : '贡献值' }}</div>
                {{ omitValue(info.value) }}
            </div>
        </div>
    </div>
</template>

<script setup>
import { reactive, ref } from 'vue';

const props = defineProps({
    info: {
        type: Object,
        default: () => { },
    },
    rank: {
        type: Number,
        default: 2,
    },
    isAnchor: {
        type: Boolean,
        default: false,
    },
});
const positionN = {
    1: ' left-1/2 translate-x-[-50%] top-14',
    2: 'left-2 top-82',
    3: 'right-2 top-82',
};
</script>

<style lang="less" scoped>
.rank-n {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-size: 100%;
    width: 120px;
    height: 192.5px;
    padding-top: 14px;

    .avatar {
        width: 69px;
        height: 69px;
        border-radius: 50%;
        position: relative;
    }

    .nickname {
        margin-top: 23px;
        line-height: 13px;
        font-size: 13px;
        color: #1300a0;
    }
    .desc {
        margin-top: 7px;
        font-size: 12px;
        color: #1300a0;
        margin-bottom: 6px;
    }
    .value {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #e30500;
        font-size: 13px;
        font-family:
            Alibaba PuHuiTi,
            Alibaba PuHuiTi-Bold;
    }
}
.rank-1 {
    background-image: url('@/assets/img/<EMAIL>');
    .nickname {
        color: #860f0f;
    }
    .desc {
        color: #860f0f;
    }
}

.rank-2 {
    background-image: url('@/assets/img/<EMAIL>');
}
.rank-3 {
    background-image: url('@/assets/img/<EMAIL>');
}
.room-status-anchor {
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
}
</style>
