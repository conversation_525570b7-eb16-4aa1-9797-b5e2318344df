<template>
    <div class="top-three">
        <rank-top-n
            v-for="item in list"
            :key="item.rank"
            :class="[positionN[rank]]"
            :info="item"
            :rank="item.rank"
            :is-anchor="isAnchor"
        />
    </div>
</template>

<script setup>
const props = defineProps({
    list: {
        type: Array,
        default: () => [],
    },
    isAnchor: {
        type: Boolean,
        default: false,
    },

});

const positionN = {
    1: 'left-140 top-30',
    2: 'left-71 top-67',
    3: 'right-71 top-67',
    4: 'left-4 top-129',
    5: 'right-4 top-129',
};
</script>

<style lang="less" scoped>
.top-three {
    position: relative;
    width: 100%;
    height: 272px;
    flex-shrink: 0;
}
</style>
