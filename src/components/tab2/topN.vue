<template>
    <div
        class="rank-n absolute flex-shrink-0 pt-7"
        :class="[positionN[rank]]"
    >
        <div class="avatar">
            <img
                :src="getAvatar(info.username)"
                class="avatar"
            />
        </div>
        <div
            class="bg-default absolute left-0 top-0 h-full w-full flex flex-col items-center pt-7"
            :class="[`rank-${rank}`]"
        >
            <div
                class="avatar"
                @click="to<PERSON>erson(info.username)"
            >
            </div>
            <div class="nickname">{{ safeOmitTxt(info.nickname, 6) }}</div>
        </div>
    </div>
</template>

<script setup>
import { reactive, ref } from 'vue';

const props = defineProps({
    info: {
        type: Object,
        default: () => { },
    },
    rank: {
        type: Number,
        default: 2,
    },
    isRankN: {
        type: Boolean,
        default: false,
    },
});
const positionN = {
    1: 'left-140 top-30',
    2: 'left-71 top-67',
    3: 'right-71 top-67',
    4: 'left-4 top-129',
    5: 'right-4 top-129',
};
</script>

<style lang="less" scoped>
.rank-n {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-size: 100% 100%;
    width: 77.5px;
    height: 111.5px;
    padding-top: 7px;

    .avatar {
        width: 46.5px;
        height: 46.5px;
        border-radius: 50%;
    }

    .nickname {
        margin-top: 13px;
        line-height: 13px;
        font-size: 13px;
        color: #ffffff;
    }
    .rank-1 {
        background-image: url('@/assets/img/<EMAIL>');
        width: 91px;
        height: 116px;

        .nickname {
            margin-top: 17px;
        }
    }

    .rank-2 {
        background-image: url('@/assets/img/<EMAIL>');
    }
    .rank-3 {
        background-image: url('@/assets/img/<EMAIL>');
    }
    .rank-4 {
        background-image: url('@/assets/img/<EMAIL>');
    }
    .rank-5 {
        background-image: url('@/assets/img/<EMAIL>');
    }
}
</style>
