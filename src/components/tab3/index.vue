<template>
    <div class="flex flex-col items-center">
        <van-list
            :finished="!rankStore.hasMore"
            offset="30"
            class="w-full"
            @load="handleLoadMore"
        >
            <div
                v-if="taskUserInfo?.uid"
                :style="{ backgroundImage: `url(${requireImg('<EMAIL>')})` }"
                class="bg-default relative h-[261px] w-[375px] flex flex-col items-center"
            >
                <div class="mt-54 text-center text-12 text-[#fff]">
                    <div>每日完成骑士团收礼值(含开通/续费)解锁奖励</div>
                </div>
                <div class="self-bg mt-2 flex items-center text-12 text-[#fff]">
                    <div class="ml-2 mr-7 h-[16.5px] w-[16.5px] rounded-full bg-[#ffffff]">
                        <img
                            class="h-full w-full rounded-full"
                            :src="getAvatar(taskUserInfo.username)"
                            alt="">
                    </div>
                    当前已完成{{ omitValue(taskValue) }}豆
                </div>
                <progress-bar
                    class="ml-[-40px] mt-10"
                    :value="taskValue"
                    :done-icon="requireImg('<EMAIL>')"
                    :un-done-icon="requireImg('<EMAIL>')"
                    :targets="[
                        {
                            value: 10000,
                            rewards: [{ ...getRewardInfo('F1'), num: 1 }],
                        },
                        {
                            value: 50000,
                            rewards: [{ ...getRewardInfo('F1'), num: 1 }],
                        },
                        {
                            value: 100000,
                            rewards: [{ ...getRewardInfo('F1'), num: 1 }],

                        },
                    ]"
                />
            </div>

            <div
                class="bg-default rank-list relative mt-19 flex flex-col items-center"
                :class="[rankStore.hasSelfRank ? 'mb-120' : '']"
            >
                <div class="text-mine mt-56 text-[11px] text-[#4B00D6]">
                    达人收到骑士团礼物(含开通/续费)的累计收礼值榜单
                </div>
                <div
                    class="bg-default mt-8 h-[116px] w-[343px] flex items-center justify-center"
                    :style="{ backgroundImage: `url(${requireImg('<EMAIL>')})` }"
                >
                    <broadcast
                        class="mx-[8px] mt-16 flex-1"
                        direction="x"
                        :allow-touch="true"
                        :data="rewardList || []">
                        <template #default="{ item }">
                            <div
                                class="bg-default mx-[4px] flex flex-shrink-0 flex-col items-center">
                                <div
                                    class="bg-default flex-center flex-center h-[72px] w-[72px] flex"
                                    :style="`background-image: url(${requireImg('<EMAIL>')})`">
                                    <img
                                        :src="item.imageUrl"
                                        class="h-80% w-80% object-contain"
                                        alt="">
                                </div>
                                <div class="mt-[6px] text-[12px] text-[#793F00] leading-[12px]">{{ item.name }}{{ item.mark === '包裹' ? `${item.price}豆` : item.mark || '' }}</div>
                            </div>
                        </template>
                    </broadcast>
                </div>
                <!-- 错误状态 -->
                <div
                    v-if="rankStore.error.hasError"
                    class="error-state">
                    <div class="error-icon">⚠️</div>
                    <div class="error-message">{{ rankStore.error.message }}</div>
                    <div
                        v-if="rankStore.error.canRetry"
                        class="retry-btn"
                        @click="handleRetry"
                    >
                        重新加载
                    </div>
                </div>

                <!-- 正常内容 -->
                <template v-else>
                    <!-- 前三名 -->
                    <top-three
                        v-if="rankStore.topThreeList.length > 0"
                        :list="rankStore.topThreeList"
                        :is-anchor="true"
                    />

                    <!-- 普通排名列表 -->
                    <div
                        v-if="rankStore.normalList.length > 0"
                        class="normal-list">
                        <rank-item
                            v-for="(item, index) in rankStore.normalList"
                            :key="`${rankStore.currentType}-${item.uid || item.id}-${index}`"
                            :item="item"
                            :rank="index + 4"
                            :type="rankStore.currentType"
                            :is-anchor="true"
                        />
                    </div>

                    <!-- 自己的排名 -->
                    <div
                        v-if="rankStore.hasSelfRank"
                        class="self-rank">
                        <rank-item
                            :is-anchor="true"
                            :item="rankStore.selfRank"
                            :rank="rankStore.selfRank.rank || rankStore.selfRank.ranking"
                            :is-self="true"
                        />
                    </div>

                    <!-- 空状态 -->
                    <div
                        v-if="rankStore.showEmptyState"
                        class="empty-state">
                        <div class="empty-text">暂无榜单数据</div>
                    </div>

                    <!-- 加载更多提示 -->
                    <div
                        v-if="rankStore.loading.more"
                        class="loading-more">
                        <div class="loading-spinner"></div>
                        <span>加载中...</span>
                    </div>

                    <!-- 没有更多数据 -->
                    <div
                        v-if="!rankStore.hasMore && rankStore.list.length > 0"
                        class="no-more">
                        <div class="no-more-line"></div>
                        <span>没有更多数据了</span>
                        <div class="no-more-line"></div>
                    </div>
                </template>
            </div>
        </van-list>
    </div>
</template>

<script setup>
import dayjs from 'dayjs';
import useRankStore from './use-anchor-rank-store';
import { getExpertDailyReceiveTBean } from '@/api';
import { getRewardInfo, normalizeUserData } from '@/auto-imports/my-utils';
import useLoading from '@/hooks/use-loading';

const rankStore = useRankStore();
useLoading(computed(() => rankStore.loading));

const taskValue = ref([]);
const taskUserInfo = ref();

const rewardList = [
    { ...getRewardInfo('B2') },
    { ...getRewardInfo('B3') },
    { ...getRewardInfo('B4') },
    { ...getRewardInfo('B5') },
    { ...getRewardInfo('C1'), name: '' },
    { ...getRewardInfo('K1') },
];
function getExpertDailyReceiveTBeanData() {
    getExpertDailyReceiveTBean().then(([res]) => {
        if (res.code !== 0) {
            return;
        }
        taskValue.value = res.data.value;
        taskUserInfo.value = res.data.userInfo;
    });
}

// 加载更多
const handleLoadMore = async () => {
    if (rankStore.loading || !rankStore.hasMore) {
        return;
    }
    try {
        await rankStore.loadMore();
    }
    catch {

    }
};

onMounted(async () => {
    getExpertDailyReceiveTBeanData();
    rankStore.retryQuery();
});
</script>

<style lang="less" scoped>
.rank-list {
    width: 100%;
    height: 100%;
    .point-9-px(url('@/assets/img/<EMAIL>'),375, 791, 750, 0, 1, 0,2);
    border-top: 0;
    border-bottom: 0;
    border-left: 0;
    border-right: 0;
    height: auto;
    min-height: 791px;
    width: 100%;
}
.self-bg {
    width: 162px;
    height: 21.5px;
    background: linear-gradient(to right, #a842ff 0%, #63b2ff 100%);
    border: 1px solid #ffeb80;
    border-radius: 12px;
}
</style>
