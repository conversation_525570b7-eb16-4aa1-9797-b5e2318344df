<template>
    <modal-container
        v-model:show="isShow"
        :close-on-click-overlay="true"
        @closed="closeModal"
    >
        <div class="video-modal">
            <div
                v-if="isShow"
                class="video-wrapper">
                <video
                    ref="mountVideo"
                    webkit-playsinline="true"
                    playsinline="true"
                    :src="videoUrl"
                    muted="true"
                    autoplay
                    loop>设备不支持动画效果</video>
                <img
                    class="absolute left-1/2 top-0 h-[459px] w-[269.5px] -translate-x-1/2"
                    src="@/assets/img/<EMAIL>"
                    alt="">
                <img
                    class="absolute bottom-[-40] left-1/2 h-[40px] w-[40px] -translate-x-1/2"
                    src="@/assets/img/<EMAIL>"
                    alt=""
                    @click="closeDialog">
            </div>
        </div>
    </modal-container>
</template>

<script setup>
const videoUrl = ref('');
const mountVideo = ref();

const isShow = ref(false);

useEventBus('video-modal').on((params) => {
    isShow.value = params.show;
    videoUrl.value = params.url;
    if (params.show) {
        if (isShow.value) {
            setTimeout(() => {
                mountVideo.value.currentTime = 0;
                mountVideo.value.muted = true;
                mountVideo.value?.play();
            }, 1000);
        }
    }
});

function closeModal() {
    isShow.value = false;
    videoUrl.value = '';
    mountVideo.value?.pause();
}

const closeDialog = () => {
    isShow.value = false;
    videoUrl.value = '';
    mountVideo.value?.pause();
};
</script>

<style lang="less" scoped>
.video-modal {
    width: 375px;
    height: auto;
    max-height: 100%;
    position: relative;
    margin: 0 auto;
    .video-wrapper {
        width: 269.5px;
        height: 459px;
        margin: 0 auto;
        video {
            width: 100%;
            max-height: 100%;
        }
    }
}
</style>
