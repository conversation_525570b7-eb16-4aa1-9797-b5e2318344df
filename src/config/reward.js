export const rewardList = [
    {
        resource_id: 'A1',
        name: '守护之心',
        type: 'user_plate',
        mark: '个人铭牌',
        unit: '天',
        special_type: '',
        image: 'A1.png',
        image_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/testing-yunying/b268-1981b51cb38.png',
        dynamic_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/channel/nameplate/ic_approve_sleb250715',
    },
    {
        resource_id: 'G1',
        name: '5天骑士团体验卡',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 10,
        special_type: '',
        image: 'G1.png',
        image_url: 'http://ga-album-cdnqn.52tt.com/act/backpack/card_knight_free.png',
    },
    {
        resource_id: 'B1',
        name: '星耀誓言',
        type: 'present_privilege',
        mark: '礼物赠送权',
        unit: '天',
        price: 10000,
        special_type: '常规礼物赠送权',
        image: 'B1.png',
        image_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/prod-yunying/0d7c-198784ae6ac.png',
    },
    {
        resource_id: 'C1',
        name: '荣耀骑士top1',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '红v认证',
        image: 'C1.png',
    },
    {
        resource_id: 'C2',
        name: '荣耀骑士top2',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '红v认证',
        image: 'C2.png',
    },
    {
        resource_id: 'C3',
        name: '荣耀骑士top3',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '红v认证',
        image: 'C3.png',
    },
    {
        resource_id: 'C4',
        name: '荣耀骑士top5',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '红v认证',
        image: 'C4.png',
    },
    {
        resource_id: 'C5',
        name: '荣耀骑士top10',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '红v认证',
        image: 'C5.png',
    },
    {
        resource_id: 'D1',
        name: '辉光圣骑',
        type: 'mount',
        mark: '坐骑',
        unit: '天',
        special_type: '',
        image: 'D1.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/c8c9-18cec15bc32-dfe79c10be45808bba4a6ca58cfc4ee5.png',
        dynamic_url: 'https://ga-album-cdnqn.52tt.com/prod-yunying/1bad-18cec15ad26.zip',
    },
    {
        resource_id: 'E1',
        name: '永恒承诺',
        type: 'headwear',
        mark: '麦位框',
        unit: '天',
        special_type: '',
        image: 'E1.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/bffc-1880b7a4649.png',
        dynamic_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/7c13-1880b7a56b6.png',
    },
    {
        resource_id: 'F1',
        name: '绯羽垂梦',
        type: 'fans_plate',
        mark: '粉丝团铭牌',
        unit: '天',
        special_type: '',
        image: 'F1.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/prod-yunying/ea03-195cbd9b1cc.webp',
    },
    {
        resource_id: 'B2',
        name: '圣骑荣光',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 131400,
        special_type: '豆豆礼物',
        image: 'B2.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/ea19-1908183179a.png',
    },
    {
        resource_id: 'B3',
        name: '未来传奇',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 52000,
        special_type: '豆豆礼物',
        image: 'B3.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/4d75-190788c0d8b.png',
    },
    {
        resource_id: 'B4',
        name: '骑士机甲',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 20000,
        special_type: '豆豆礼物',
        image: 'B4.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/e616-192e6a0282e.png',
    },
    {
        resource_id: 'B5',
        name: '守护相伴',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 10000,
        special_type: '豆豆礼物',
        image: 'B5.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/14b9-19081cca79f.png',
    },
    {
        resource_id: 'H1',
        name: '骑士团守护榜达人top1',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'H1.png',
    },
    {
        resource_id: 'H2',
        name: '骑士团守护榜达人top2',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'H2.png',
    },
    {
        resource_id: 'H3',
        name: '骑士团守护榜达人top3',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'H3.png',
    },
    {
        resource_id: 'H4',
        name: '骑士团守护榜达人top5',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'H4.png',
    },
    {
        resource_id: 'H5',
        name: '骑士团守护榜达人top10',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'H5.png',
    },
    {
        resource_id: 'K1',
        name: '万团殿堂',
        type: 'other',
        mark: '房间背景',
        unit: '天',
        special_type: '',
        image: 'K1.png',
        image_url: 'https://tt-ugc-cdnqn.52tt.com/images-oss/147-111605220-e49333502f639983f0a99e8677d734d1',
    },
];

export const rewardMap = {
    A1: {
        resource_id: 'A1',
        name: '守护之心',
        type: 'user_plate',
        mark: '个人铭牌',
        unit: '天',
        special_type: '',
        image: 'A1.png',
        image_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/testing-yunying/b268-1981b51cb38.png',
        dynamic_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/channel/nameplate/ic_approve_sleb250715',
    },
    G1: {
        resource_id: 'G1',
        name: '5天骑士团体验卡',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 10,
        special_type: '',
        image: 'G1.png',
        image_url: 'http://ga-album-cdnqn.52tt.com/act/backpack/card_knight_free.png',
    },
    B1: {
        resource_id: 'B1',
        name: '星耀誓言',
        type: 'present_privilege',
        mark: '礼物赠送权',
        unit: '天',
        price: 10000,
        special_type: '常规礼物赠送权',
        image: 'B1.png',
        image_url: 'https://obs-cdn.52tt.com/tt/oper-backstage/prod-yunying/0d7c-198784ae6ac.png',
    },
    C1: {
        resource_id: 'C1',
        name: '荣耀骑士top1',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '红v认证',
        image: 'C1.png',
    },
    C2: {
        resource_id: 'C2',
        name: '荣耀骑士top2',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '红v认证',
        image: 'C2.png',
    },
    C3: {
        resource_id: 'C3',
        name: '荣耀骑士top3',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '红v认证',
        image: 'C3.png',
    },
    C4: {
        resource_id: 'C4',
        name: '荣耀骑士top5',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '红v认证',
        image: 'C4.png',
    },
    C5: {
        resource_id: 'C5',
        name: '荣耀骑士top10',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '红v认证',
        image: 'C5.png',
    },
    D1: {
        resource_id: 'D1',
        name: '辉光圣骑',
        type: 'mount',
        mark: '坐骑',
        unit: '天',
        special_type: '',
        image: 'D1.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/c8c9-18cec15bc32-dfe79c10be45808bba4a6ca58cfc4ee5.png',
        dynamic_url: 'https://ga-album-cdnqn.52tt.com/prod-yunying/1bad-18cec15ad26.zip',
    },
    E1: {
        resource_id: 'E1',
        name: '永恒承诺',
        type: 'headwear',
        mark: '麦位框',
        unit: '天',
        special_type: '',
        image: 'E1.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/bffc-1880b7a4649.png',
        dynamic_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/7c13-1880b7a56b6.png',
    },
    F1: {
        resource_id: 'F1',
        name: '绯羽垂梦',
        type: 'fans_plate',
        mark: '粉丝团铭牌',
        unit: '天',
        special_type: '',
        image: 'F1.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/prod-yunying/ea03-195cbd9b1cc.webp',
    },
    B2: {
        resource_id: 'B2',
        name: '圣骑荣光',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 131400,
        special_type: '豆豆礼物',
        image: 'B2.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/ea19-1908183179a.png',
    },
    B3: {
        resource_id: 'B3',
        name: '未来传奇',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 52000,
        special_type: '豆豆礼物',
        image: 'B3.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/4d75-190788c0d8b.png',
    },
    B4: {
        resource_id: 'B4',
        name: '骑士机甲',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 20000,
        special_type: '豆豆礼物',
        image: 'B4.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/e616-192e6a0282e.png',
    },
    B5: {
        resource_id: 'B5',
        name: '守护相伴',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 10000,
        special_type: '豆豆礼物',
        image: 'B5.png',
        image_url: 'https://ga-album-cdnqn.52tt.com/testing-yunying/14b9-19081cca79f.png',
    },
    H1: {
        resource_id: 'H1',
        name: '骑士团守护榜达人top1',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'H1.png',
    },
    H2: {
        resource_id: 'H2',
        name: '骑士团守护榜达人top2',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'H2.png',
    },
    H3: {
        resource_id: 'H3',
        name: '骑士团守护榜达人top3',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'H3.png',
    },
    H4: {
        resource_id: 'H4',
        name: '骑士团守护榜达人top5',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'H4.png',
    },
    H5: {
        resource_id: 'H5',
        name: '骑士团守护榜达人top10',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'H5.png',
    },
    K1: {
        resource_id: 'K1',
        name: '万团殿堂',
        type: 'other',
        mark: '房间背景',
        unit: '天',
        special_type: '',
        image: 'K1.png',
        image_url: 'https://tt-ugc-cdnqn.52tt.com/images-oss/147-111605220-e49333502f639983f0a99e8677d734d1',
    },
};
