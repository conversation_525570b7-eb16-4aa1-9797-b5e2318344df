<route lang="json">
{
    "name": "Home",
    "path": "/:pathMatch(.*)*",
    "meta": {
        "title": "首页"
    }
}
</route>

<template>
    <page-container>
        <div class="home pt-149">
            <img
                class="absolute right-0 top-14 z-100 h-[19px] w-[63px]"
                :src="requireImg('<EMAIL>')"
                @click="jumpToLink('/web/frontend-web-activity-sub-venue-3-2507/index.html')"
            >
            <tab class="mb-20"></tab>
            <tab1 v-if="navStore.current === 1"></tab1>
            <tab2 v-if="navStore.current === 2"></tab2>
            <tab3 v-if="navStore.current === 3"></tab3>
            <video-modal></video-modal>
            <record-modal></record-modal>
        </div>
    </page-container>
</template>

<script setup name="Home">
import useInitStore from '@/stores/modules/use-init-store';
import useNavStore from '@/components/tab/use-nav';

const navStore = useNavStore();

const router = useRouter();
const initStore = useInitStore();

const toRule = () => {
    router.push('/rule');
    // 打开分享弹窗
    useEventBus('share-modal').emit({ show: true });
};

onMounted(async () => {
    const toast = showLoading();
    await initStore.init();
    toast.close();
});
</script>

<style lang="less" scoped>
.home {
    min-height: 100%;
    background:
        url('@/assets/img/<EMAIL>') no-repeat,
        #6e31de;
    background-size:
        375px 253px,
        100% 100%;
    background-position:
        0 0,
        0 0;
    position: relative;
    box-sizing: border-box;
}
</style>
